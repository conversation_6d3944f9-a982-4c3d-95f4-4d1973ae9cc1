import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  StyleSheet,
  View,
  BackHandler,
  Alert,
  Text,
  StatusBar,
  Animated,
  Platform,
} from 'react-native';
import ChattingSection from '../Components/ChattingSection';
import TextInputSection from '../Components/TextInputSection';
import ErrorBoundary from '../Components/ErrorBoundary';
import MessageStatus from '../Constants/MessageStatus';
import SocketService, {
  isRoomPageActive,
  setIsRoomPage,
} from '../services/socketIoService';
import { setupNotificationHandlers } from '../services/notificationService';
import ReconnectButton from '../Components/Buttons/ReconnectButton.js';
import BackgroundSection from '../Components/BackgroundSection';
import { sendImage } from '../util/ImageHandler';
import * as filter from 'leo-profanity';
import { useRecoilState, useSetRecoilState, useRecoilValue } from 'recoil';
import { messageSelector, partnerSelector } from '../store/session/selectors';
import { sessionState } from '../store/session/atoms';
import { reconnectWithAdLogic } from '../utils/reconnectUtils';
import { useIsAdFree } from '../contexts/IAPContext';
import Tooltip from 'react-native-walkthrough-tooltip';
import { LinksState, RoomBadgeState, TooltipState } from '../store/tooltip/atoms';
import { Stage } from '../store/tooltip/enum';
import { useFocusEffect } from '@react-navigation/native';

const TypingIndicator = () => {
  const dotAnimation = useRef([
    new Animated.Value(0),
    new Animated.Value(0),
    new Animated.Value(0),
  ]).current;

  useEffect(() => {
    const animations = dotAnimation.map((dot, index) =>
      Animated.sequence([
        Animated.delay(index * 200),
        Animated.loop(
          Animated.sequence([
            Animated.timing(dot, {
              toValue: 1,
              duration: 400,
              useNativeDriver: true,
            }),
            Animated.timing(dot, {
              toValue: 0,
              duration: 400,
              useNativeDriver: true,
            }),
          ]),
        ),
      ]),
    );

    Animated.parallel(animations).start();

    return () => animations.forEach(anim => anim.stop());
  }, [dotAnimation]);

  return (
    <View style={styles.typingContainer}>
      <Text style={styles.typingText}>Typing</Text>
      {dotAnimation.map((dot, index) => (
        <Animated.View
          key={index}
          style={[
            styles.dot,
            {
              transform: [
                {
                  translateY: dot.interpolate({
                    inputRange: [0, 1],
                    outputRange: [0, -6],
                  }),
                },
              ],
              opacity: dot.interpolate({
                inputRange: [0, 1],
                outputRange: [0.3, 1],
              }),
            },
          ]}
        />
      ))}
    </View>
  );
};

export default function Room(props) {
  const [toolTipStage, setToolTipStage] = useRecoilState(TooltipState);
  const [roomBadge, setRoomBadge] = useRecoilState(RoomBadgeState);
  const [links, setLinks] = useRecoilState(LinksState);
  const [messages, setMessages] = useRecoilState(messageSelector);
  const session = useRecoilValue(sessionState);
  const isAdFree = useIsAdFree();
  const [userIsTyping, setUserIsTyping] = useState(false);
  const partner = useRecoilValue(partnerSelector());
  let getID = seq => Math.floor(Math.random() * 1000) + '-' + seq;
  const messagesRef = useRef(messages);
  const setPartner = useSetRecoilState(partnerSelector());


  useEffect(() => {
    if (userIsTyping) {
      setTimeout(function () {
        setUserIsTyping(false);
      }, 2000);
    }
  }, [userIsTyping]);

  useFocusEffect(
    React.useCallback(() => {
      setIsRoomPage(true);
      if (roomBadge) {
        setRoomBadge(false);
      }
      return () => {
        setIsRoomPage(false);
      };
    }, [roomBadge, setRoomBadge]),
  );

  useFocusEffect(
    useCallback(() => {
      const handleBackButtonPress = () => {
        Alert.alert('Hold on!', 'Are you sure you want to Exit?', [
          {
            text: 'Cancel',
            onPress: () => null,
            style: 'cancel',
          },
          {
            text: 'YES',
            onPress: () => {
              onDisconnect();
              BackHandler.exitApp();
            },
          },
        ]);
        return true;
      };

      BackHandler.addEventListener('hardwareBackPress', handleBackButtonPress);

      return () =>
        BackHandler.removeEventListener(
          'hardwareBackPress',
          handleBackButtonPress,
        );
    }, []),
  );

  useEffect(() => {
    SocketService.init(links, setLinks);
    SocketService.ack(updateMessage);
    SocketService.serverMessage(addServerMessage, setPartner);
    SocketService.userTyping(setUserIsTyping);
    SocketService.disconnect(addServerMessage, setPartner, setUserIsTyping);

    // Set up reconnect callback for unified ad logic
    SocketService.setReconnectCallback(() => {
      reconnectWithAdLogic(session.partners, isAdFree);
    });

    // Setup notification handlers with addUserMessage callback
    setupNotificationHandlers(addSentUserMessage);
  }, [session.partners, isAdFree]);

  useEffect(() => {
    if (props.route.params?.img) {
      handleFiles(props.route.params.img);
    }
  }, [props.route.params?.img, handleFiles]);

  useEffect(() => {
    messagesRef.current = messages;
  });


  const updateMessage = useCallback(
    function (id, attr, value) {
      let index = messagesRef.current.findIndex(element => {
        return id === element.id;
      });

      if (index !== -1) {
        let items = [...messagesRef.current]; // Create a shallow copy of the messages
        items[index] = { ...items[index], [attr]: value }; // Create a new object for the message
        setMessages(items); // Update the state with the new array
      }
    },
    [setMessages],
  );

  let galaryPick = () => {
    props.navigation.navigate('Gallary');
  };

  let cameraOpen = () => {
    props.navigation.navigate('Camera');
  };

  let addToMessages = data => {
    let text = filter.clean(data);
    let message = addUserMessage({ text });
    SocketService.chat(message);
  };

  function addSentUserMessage(data) {
    let text = filter.clean(data);
    let message = addUserMessage({ text, status: MessageStatus.sent });
    return message;
  }
  let addUserMessage = useCallback(
    data => {
      let id = appendMessages({
        status: MessageStatus.pending,
        ...data,
      });
      return { ...data, id: id };
    },
    [appendMessages],
  );

  let onDisconnect = () => {
    SocketService.emit('logout');
  };

  let aboutUs = () => {
    props.navigation.navigate('About Us');
  };

  let appendMessages = useCallback(
    data => {
      let id = getID(messagesRef.current.length);
      setMessages(oldMessages => [
        ...oldMessages,
        {
          id: id,
          time: new Date().toLocaleTimeString([], {
            hour: '2-digit',
            minute: '2-digit',
          }),
          ...data,
        },
      ]);
      return id;
    },
    [setMessages],
  );

  let addServerMessage = useCallback(
    data => {
      appendMessages(data);
      if (isRoomPageActive === roomBadge) {
        setRoomBadge(!isRoomPageActive);
      }
    },
    [appendMessages, roomBadge, setRoomBadge],
  );

  const handleFiles = useCallback(
    img => {
      let base64 = img.base64;
      if (base64) {
        let msg = addUserMessage({ uri: img.uri, progress: 0 });
        sendImage(img.uri.split('.').pop(), base64, msg.id, updateMessage);
      }
    },
    [addUserMessage, updateMessage],
  );

  let showSettings = () => {
    props.navigation.navigate('Settings');
  };

  return (
    <View style={styles.safeArea}>
      <StatusBar barStyle="dark-content" backgroundColor="#fff" />
      <BackgroundSection>
        <View style={styles.container}>

          <Tooltip
            isVisible={toolTipStage === Stage.History}
            content={
              <Text style={styles.tooltipText}>
                Let's open the settings to set your avatar
              </Text>
            }
            placement="center"
            displayInsets={{ top: 0, bottom: 0, left: 0, right: 0 }}
            accessible={false}
            useInteractionManager={true}
            onClose={() => {
              showSettings();
              setToolTipStage(Stage.Avatar);
            }}
          />

          <View style={styles.content}>
            <ErrorBoundary>
              <ChattingSection style={styles.chattingArea} />
            </ErrorBoundary>
            {userIsTyping && <TypingIndicator />}
            <ReconnectButton />
          </View>

          <TextInputSection
            onSubmit={addToMessages}
            onAddingPhoto={galaryPick}
            onCameraShot={cameraOpen}
            menuItems={[
              ['End Chat', onDisconnect],
              ['Settings', showSettings],
              ['About Us', aboutUs],
            ]}
          />
        </View>
      </BackgroundSection>
    </View>
  );

}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#fff',
  },
  container: {
    flex: 1,
  },
  header: {
    backgroundColor: '#fff',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)',
    flexDirection: 'column',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C2C2C',
    fontFamily:
      Platform.OS === 'ios' ? 'Poppins-Regular' : 'PoppinsRegular-B2Bw',
  },
  headerSubtitle: {
    fontSize: 13,
    color: '#666',
    marginTop: 2,
    fontFamily:
      Platform.OS === 'ios' ? 'Poppins-Regular' : 'PoppinsRegular-B2Bw',
  },
  content: {
    flex: 1,
    position: 'relative',
  },
  chattingArea: {
    flexGrow: 1,
    flexShrink: 1,
    flexBasis: 'auto',
  },
  tooltipText: {
    color: '#2C2C2C',
    fontSize: 14,
    fontFamily:
      Platform.OS === 'ios' ? 'Poppins-Regular' : 'PoppinsRegular-B2Bw',
  },
  typingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    position: 'absolute',
    bottom: 0,
    left: 16,
    backgroundColor: 'rgba(255,255,255,0.9)',
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.08,
    shadowRadius: 2,
    elevation: 2,
  },
  typingText: {
    fontSize: 13,
    color: '#666',
    marginRight: 4,
    fontFamily:
      Platform.OS === 'ios' ? 'Poppins-Regular' : 'PoppinsRegular-B2Bw',
  },
  dot: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: '#666',
    marginHorizontal: 2,
  },
});
